<?php get_header(); ?>

<div class="hero-section">
    <h1 class="hero-title">Welcome to StreamFlix</h1>
    <p class="hero-subtitle">Discover and stream the latest movies and TV shows</p>
</div>

<section class="featured-content">
    <div class="section-header">
        <span class="section-icon">⭐</span>
        <h2 class="section-title">Featured Movies</h2>
    </div>
    
    <div id="featuredMovies" class="content-grid">
        <div class="loading">Loading featured movies...</div>
    </div>
</section>

<section class="popular-tv">
    <div class="section-header">
        <span class="section-icon">📺</span>
        <h2 class="section-title">Popular TV Shows</h2>
    </div>
    
    <div id="popularTV" class="content-grid">
        <div class="loading">Loading popular TV shows...</div>
    </div>
</section>

<script>
jQuery(document).ready(function($) {
    // Load featured movies
    loadContent('movies', 1, '#featuredMovies');
    
    // Load popular TV shows
    loadContent('tv', 1, '#popularTV');
    
    function loadContent(type, page, container) {
        $.ajax({
            url: streamflix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'streamflix_load_more',
                type: type,
                page: page,
                nonce: streamflix_ajax.nonce
            },
            success: function(response) {
                if (response) {
                    $(container).html(response);
                    bindContentItemEvents();
                }
            },
            error: function() {
                $(container).html('<p>Error loading content. Please try again.</p>');
            }
        });
    }
    
    function bindContentItemEvents() {
        $('.content-item').off('click').on('click', function() {
            var id = $(this).data('id');
            var type = $(this).data('type');
            openModal(id, type);
        });
    }
    
    function openModal(id, type) {
        var endpoint = type === 'movies' ? '/movie/' + id : '/tv/' + id;
        
        $.ajax({
            url: 'https://api.themoviedb.org/3' + endpoint + '?api_key=3308647fabe47a844ab269e6eab19132',
            type: 'GET',
            success: function(data) {
                var title = data.title || data.name;
                var overview = data.overview;
                var posterPath = data.poster_path ? 'https://image.tmdb.org/t/p/w500' + data.poster_path : '';
                var releaseDate = data.release_date || data.first_air_date;
                var rating = Math.round(data.vote_average * 10) / 10;
                var runtime = data.runtime || (data.episode_run_time && data.episode_run_time[0]) || 'N/A';
                
                var modalContent = `
                    <h2 class="modal-title">${title}</h2>
                    <div class="modal-details">
                        <img src="${posterPath}" alt="${title}" class="modal-poster">
                        <div class="modal-info">
                            <p><strong>Release Date:</strong> ${releaseDate}</p>
                            <p><strong>Rating:</strong> ⭐ ${rating}/10</p>
                            <p><strong>Runtime:</strong> ${runtime} minutes</p>
                        </div>
                    </div>
                    <div class="modal-overview">
                        <p>${overview}</p>
                    </div>
                    <button class="watch-button" onclick="watchContent(${id}, '${type}')">Watch Now</button>
                `;
                
                $('#modalContent').html(modalContent);
                $('#contentModal').show();
            }
        });
    }
    
    // Close modal
    $('.close, .modal').on('click', function(e) {
        if (e.target === this) {
            $('#contentModal').hide();
        }
    });
});

function watchContent(id, type) {
    var streamUrl = type === 'movies' 
        ? `https://multiembed.mov/?video_id=${id}&tmdb=1`
        : `https://multiembed.mov/?video_id=${id}&tmdb=1&s=1&e=1`;
    
    window.open(streamUrl, '_blank');
}
</script>

<?php get_footer(); ?>
