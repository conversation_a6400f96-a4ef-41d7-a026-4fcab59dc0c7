<?php get_header(); ?>

<div class="hero-section">
    <h1 class="hero-title">Welcome to StreamFlix</h1>
    <p class="hero-subtitle">Discover and stream the latest movies and TV shows</p>
</div>

<section class="featured-content">
    <div class="section-header">
        <span class="section-icon">⭐</span>
        <h2 class="section-title">Featured Movies</h2>
    </div>
    
    <div id="featuredMovies" class="content-grid">
        <div class="loading">Loading featured movies...</div>
    </div>
</section>

<section class="popular-tv">
    <div class="section-header">
        <span class="section-icon">📺</span>
        <h2 class="section-title">Popular TV Shows</h2>
    </div>
    
    <div id="popularTV" class="content-grid">
        <div class="loading">Loading popular TV shows...</div>
    </div>
</section>

<script>
jQuery(document).ready(function($) {
    // Load featured movies
    loadContent('movies', 1, '#featuredMovies');
    
    // Load popular TV shows
    loadContent('tv', 1, '#popularTV');
    
    function loadContent(type, page, container) {
        $.ajax({
            url: streamflix_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'streamflix_load_more',
                type: type,
                page: page,
                nonce: streamflix_ajax.nonce
            },
            success: function(response) {
                if (response) {
                    $(container).html(response);
                    bindContentItemEvents();
                }
            },
            error: function() {
                $(container).html('<p>Error loading content. Please try again.</p>');
            }
        });
    }
    
    function bindContentItemEvents() {
        $('.content-item').off('click').on('click', function() {
            var id = $(this).data('id');
            var type = $(this).data('type');
            openModal(id, type);
        });
    }
    
    function openModal(id, type) {
        // Redirect to detail page instead of opening modal
        const detailUrl = `${window.location.origin}${window.location.pathname}?content=detail&id=${id}&type=${type}`;
        window.location.href = detailUrl;
    }
    
    // Close modal
    $('.close, .modal').on('click', function(e) {
        if (e.target === this) {
            $('#contentModal').hide();
        }
    });
});

function watchContent(id, type) {
    var streamUrl = type === 'movies' 
        ? `https://multiembed.mov/?video_id=${id}&tmdb=1`
        : `https://multiembed.mov/?video_id=${id}&tmdb=1&s=1&e=1`;
    
    window.open(streamUrl, '_blank');
}
</script>

<?php get_footer(); ?>
