<?php get_header(); ?>

<div class="page-header">
    <div class="section-header">
        <span class="section-icon">🎬</span>
        <h1 class="section-title">Movies</h1>
    </div>
    <p class="page-description">Discover the latest and most popular movies</p>
</div>

<div class="filter-section">
    <div class="filter-controls">
        <select id="movieGenre" class="filter-select">
            <option value="">All Genres</option>
            <option value="28">Action</option>
            <option value="12">Adventure</option>
            <option value="16">Animation</option>
            <option value="35">Comedy</option>
            <option value="80">Crime</option>
            <option value="99">Documentary</option>
            <option value="18">Drama</option>
            <option value="10751">Family</option>
            <option value="14">Fantasy</option>
            <option value="36">History</option>
            <option value="27">Horror</option>
            <option value="10402">Music</option>
            <option value="9648">Mystery</option>
            <option value="10749">Romance</option>
            <option value="878">Science Fiction</option>
            <option value="10770">TV Movie</option>
            <option value="53">Thriller</option>
            <option value="10752">War</option>
            <option value="37">Western</option>
        </select>
        
        <select id="movieSort" class="filter-select">
            <option value="popularity.desc">Most Popular</option>
            <option value="release_date.desc">Latest Release</option>
            <option value="vote_average.desc">Highest Rated</option>
            <option value="revenue.desc">Highest Grossing</option>
        </select>
        
        <select id="movieYear" class="filter-select">
            <option value="">All Years</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
            <option value="2021">2021</option>
            <option value="2020">2020</option>
        </select>
    </div>
</div>

<section class="movies-content">
    <div id="moviesGrid" class="content-grid">
        <div class="loading">Loading movies...</div>
    </div>
    
    <div class="load-more-container">
        <button id="loadMoreMovies" class="load-more-btn" style="display: none;">Load More Movies</button>
    </div>
</section>

<script>
jQuery(document).ready(function($) {
    let currentPage = 1;
    let isLoading = false;
    let hasMoreContent = true;
    
    // Load initial movies
    loadMovies(1, true);
    
    // Filter change handlers
    $('#movieGenre, #movieSort, #movieYear').on('change', function() {
        currentPage = 1;
        hasMoreContent = true;
        loadMovies(1, true);
    });
    
    // Load more button
    $('#loadMoreMovies').on('click', function() {
        if (!isLoading && hasMoreContent) {
            currentPage++;
            loadMovies(currentPage, false);
        }
    });
    
    function loadMovies(page, replace = false) {
        if (isLoading) return;
        
        isLoading = true;
        $('#loadMoreMovies').text('Loading...').prop('disabled', true);
        
        if (replace) {
            $('#moviesGrid').html('<div class="loading">Loading movies...</div>');
        }
        
        const genre = $('#movieGenre').val();
        const sort = $('#movieSort').val();
        const year = $('#movieYear').val();
        
        let url = `https://api.themoviedb.org/3/discover/movie?api_key=3308647fabe47a844ab269e6eab19132&page=${page}&sort_by=${sort}`;
        
        if (genre) {
            url += `&with_genres=${genre}`;
        }
        
        if (year) {
            url += `&year=${year}`;
        }
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(data) {
                if (data && data.results) {
                    let moviesHtml = '';
                    
                    data.results.forEach(function(movie) {
                        const title = movie.title;
                        const year = movie.release_date ? new Date(movie.release_date).getFullYear() : 'N/A';
                        const posterPath = movie.poster_path ? 
                            'https://image.tmdb.org/t/p/w500' + movie.poster_path : 
                            '<?php echo get_template_directory_uri(); ?>/images/no-poster.jpg';
                        const rating = Math.round(movie.vote_average * 10) / 10;
                        
                        moviesHtml += `
                            <div class="content-item" data-id="${movie.id}" data-type="movies">
                                <img src="${posterPath}" alt="${title}" class="content-poster">
                                <div class="content-info">
                                    <h3 class="content-title">${title}</h3>
                                    <div class="content-year">${year}</div>
                                    <div class="content-rating">⭐ ${rating}/10</div>
                                </div>
                            </div>
                        `;
                    });
                    
                    if (replace) {
                        $('#moviesGrid').html(moviesHtml);
                    } else {
                        $('#moviesGrid').append(moviesHtml);
                    }
                    
                    bindContentItemEvents();
                    
                    // Check if there are more pages
                    hasMoreContent = page < data.total_pages;
                    
                    if (hasMoreContent) {
                        $('#loadMoreMovies').show().text('Load More Movies').prop('disabled', false);
                    } else {
                        $('#loadMoreMovies').hide();
                    }
                } else {
                    if (replace) {
                        $('#moviesGrid').html('<p>No movies found.</p>');
                    }
                }
                
                isLoading = false;
            },
            error: function() {
                $('#moviesGrid').html('<p>Error loading movies. Please try again.</p>');
                isLoading = false;
            }
        });
    }
    
    function bindContentItemEvents() {
        $('.content-item').off('click').on('click', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            openModal(id, type);
        });
    }
    
    function openModal(id, type) {
        // Redirect to detail page instead of opening modal
        const detailUrl = `${window.location.origin}${window.location.pathname}?content=detail&id=${id}&type=${type}`;
        window.location.href = detailUrl;
    }
    
    // Close modal
    $('.close, .modal').on('click', function(e) {
        if (e.target === this) {
            $('#contentModal').hide();
        }
    });
});

function watchContent(id, type) {
    const streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1`;
    window.open(streamUrl, '_blank');
}
</script>

<?php get_footer(); ?>
