<?php
/**
 * StreamFlix Theme Functions
 */

// Theme setup
function streamflix_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'streamflix'),
    ));
}
add_action('after_setup_theme', 'streamflix_setup');

// Enqueue styles and scripts
function streamflix_scripts() {
    wp_enqueue_style('streamflix-style', get_stylesheet_uri());
    wp_enqueue_script('streamflix-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('streamflix-script', 'streamflix_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('streamflix_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'streamflix_scripts');

// TMDB API Configuration
define('TMDB_API_KEY', '3308647fabe47a844ab269e6eab19132');
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE_URL', 'https://image.tmdb.org/t/p/w500');

// Function to make TMDB API requests
function tmdb_api_request($endpoint, $params = array()) {
    $params['api_key'] = TMDB_API_KEY;
    $url = TMDB_BASE_URL . $endpoint . '?' . http_build_query($params);
    
    $response = wp_remote_get($url);
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    return json_decode($body, true);
}

// Get popular movies
function get_popular_movies($page = 1) {
    return tmdb_api_request('/movie/popular', array('page' => $page));
}

// Get popular TV shows
function get_popular_tv_shows($page = 1) {
    return tmdb_api_request('/tv/popular', array('page' => $page));
}

// Get movie details
function get_movie_details($movie_id) {
    return tmdb_api_request('/movie/' . $movie_id);
}

// Get TV show details
function get_tv_show_details($tv_id) {
    return tmdb_api_request('/tv/' . $tv_id);
}

// Search movies and TV shows
function search_tmdb($query, $page = 1) {
    return tmdb_api_request('/search/multi', array('query' => $query, 'page' => $page));
}

// AJAX handler for loading more content
function streamflix_load_more_content() {
    check_ajax_referer('streamflix_nonce', 'nonce');
    
    $type = sanitize_text_field($_POST['type']);
    $page = intval($_POST['page']);
    
    if ($type === 'movies') {
        $data = get_popular_movies($page);
    } elseif ($type === 'tv') {
        $data = get_popular_tv_shows($page);
    } else {
        wp_die();
    }
    
    if ($data && isset($data['results'])) {
        foreach ($data['results'] as $item) {
            echo render_content_item($item, $type);
        }
    }
    
    wp_die();
}
add_action('wp_ajax_streamflix_load_more', 'streamflix_load_more_content');
add_action('wp_ajax_nopriv_streamflix_load_more', 'streamflix_load_more_content');

// AJAX handler for search
function streamflix_search() {
    check_ajax_referer('streamflix_nonce', 'nonce');
    
    $query = sanitize_text_field($_POST['query']);
    $page = intval($_POST['page']);
    
    $data = search_tmdb($query, $page);
    
    if ($data && isset($data['results'])) {
        foreach ($data['results'] as $item) {
            $type = isset($item['title']) ? 'movies' : 'tv';
            echo render_content_item($item, $type);
        }
    }
    
    wp_die();
}
add_action('wp_ajax_streamflix_search', 'streamflix_search');
add_action('wp_ajax_nopriv_streamflix_search', 'streamflix_search');

// Render content item HTML
function render_content_item($item, $type) {
    $title = $type === 'movies' ? $item['title'] : $item['name'];
    $date = $type === 'movies' ? $item['release_date'] : $item['first_air_date'];
    $year = $date ? date('Y', strtotime($date)) : 'N/A';
    $poster_path = $item['poster_path'] ? TMDB_IMAGE_BASE_URL . $item['poster_path'] : get_template_directory_uri() . '/images/no-poster.jpg';
    $rating = round($item['vote_average'], 1);
    
    ob_start();
    ?>
    <div class="content-item" data-id="<?php echo $item['id']; ?>" data-type="<?php echo $type; ?>">
        <img src="<?php echo esc_url($poster_path); ?>" alt="<?php echo esc_attr($title); ?>" class="content-poster">
        <div class="content-info">
            <h3 class="content-title"><?php echo esc_html($title); ?></h3>
            <div class="content-year"><?php echo esc_html($year); ?></div>
            <div class="content-rating">★ <?php echo esc_html($rating); ?>/10</div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

// Get SuperEmbed streaming URL
function get_superembed_url($tmdb_id, $type) {
    if ($type === 'movies') {
        return "https://multiembed.mov/?video_id={$tmdb_id}&tmdb=1";
    } else {
        return "https://multiembed.mov/?video_id={$tmdb_id}&tmdb=1&s=1&e=1";
    }
}

// AJAX handler for getting streaming URL
function streamflix_get_stream_url() {
    check_ajax_referer('streamflix_nonce', 'nonce');
    
    $tmdb_id = sanitize_text_field($_POST['tmdb_id']);
    $type = sanitize_text_field($_POST['type']);
    
    $stream_url = get_superembed_url($tmdb_id, $type);
    
    wp_send_json_success(array('stream_url' => $stream_url));
}
add_action('wp_ajax_streamflix_get_stream_url', 'streamflix_get_stream_url');
add_action('wp_ajax_nopriv_streamflix_get_stream_url', 'streamflix_get_stream_url');

// Custom page templates
function streamflix_page_template($template) {
    if (is_page('movies')) {
        $new_template = locate_template(array('page-movies.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }

    if (is_page('tv-shows')) {
        $new_template = locate_template(array('page-tv-shows.php'));
        if (!empty($new_template)) {
            return $new_template;
        }
    }

    return $template;
}
add_filter('page_template', 'streamflix_page_template');

// Handle detail page routing
function streamflix_template_redirect() {
    if (isset($_GET['content']) && isset($_GET['id']) && isset($_GET['type'])) {
        include(get_template_directory() . '/single-content.php');
        exit;
    }
}
add_action('template_redirect', 'streamflix_template_redirect');

// Create default pages on theme activation
function streamflix_create_pages() {
    // Create Movies page
    $movies_page = get_page_by_title('Movies');
    if (!$movies_page) {
        wp_insert_post(array(
            'post_title' => 'Movies',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'movies'
        ));
    }
    
    // Create TV Shows page
    $tv_page = get_page_by_title('TV Shows');
    if (!$tv_page) {
        wp_insert_post(array(
            'post_title' => 'TV Shows',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'tv-shows'
        ));
    }
}
add_action('after_switch_theme', 'streamflix_create_pages');
?>
