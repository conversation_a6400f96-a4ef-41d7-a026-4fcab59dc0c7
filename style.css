/*
Theme Name: StreamFlix
Description: A modern streaming website theme for movies and TV shows
Version: 1.0
Author: StreamFlix Team
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #0f1419;
    color: #ffffff;
    line-height: 1.6;
}

/* Header Styles */
.site-header {
    background: rgba(26, 31, 46, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    position: relative;
}

.site-logo {
    font-size: 2rem;
    font-weight: bold;
    color: #4f46e5;
    text-decoration: none;
    transition: color 0.3s ease;
}

.site-logo:hover {
    color: #6366f1;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    background: none;
    border: none;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
    padding: 0;
}

.main-nav a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
}

.main-nav a:hover {
    background-color: rgba(79, 70, 229, 0.2);
    color: #ffffff;
    transform: translateY(-1px);
}

.search-form {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-input {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    width: 200px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4f46e5;
    background-color: rgba(255, 255, 255, 0.15);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-button {
    padding: 0.75rem 1.25rem;
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* Main Content */
.main-content {
    margin-top: 80px;
    padding: 3rem 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}



/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.content-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(79, 70, 229, 0.2);
    border-color: rgba(79, 70, 229, 0.3);
}

.content-poster {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.content-info {
    padding: 1rem;
}

.content-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.content-year {
    color: #1e3a8a;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.content-rating {
    color: #cccccc;
    font-size: 0.8rem;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border-radius: 2px;
}

.section-icon {
    color: #4f46e5;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.section-title {
    font-size: 2rem;
    color: #ffffff;
    font-weight: 600;
}

/* Loading Spinner */
.loading {
    text-align: center;
    padding: 2rem;
    color: #4f46e5;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 1.5rem;
    }

    .search-input {
        width: 180px;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .main-nav {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        background: rgba(26, 31, 46, 0.98);
        backdrop-filter: blur(20px);
        transition: right 0.3s ease;
        padding-top: 80px;
        border-left: 1px solid rgba(255, 255, 255, 0.1);
    }

    .main-nav.active {
        right: 0;
    }

    .main-nav ul {
        flex-direction: column;
        gap: 0;
        padding: 2rem;
    }

    .main-nav li {
        width: 100%;
    }

    .main-nav a {
        display: block;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 0;
        transition: all 0.3s ease;
    }

    .main-nav a:hover {
        background-color: rgba(79, 70, 229, 0.2);
        transform: translateX(5px);
    }

    .search-form {
        order: -1;
        margin-bottom: 1rem;
        width: 100%;
        max-width: 300px;
    }

    .search-input {
        width: 100%;
        flex: 1;
    }



    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .main-content {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: 0 1rem;
    }

    .site-logo {
        font-size: 1.6rem;
    }



    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .search-input {
        font-size: 14px;
    }

    .search-button {
        padding: 0.75rem 1rem;
        font-size: 14px;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: #1a1f2e;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #4f46e5;
}

.modal-title {
    color: #4f46e5;
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.modal-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.modal-poster {
    width: 200px;
    border-radius: 10px;
}

.modal-info {
    flex: 1;
}

.modal-overview {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.watch-button {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.watch-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

/* Search Dropdown Styles */
.search-form {
    position: relative;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #1a1f2e;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.search-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #2a2f3e;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-item:hover {
    background-color: #2a2f3e;
}

.search-item:last-child {
    border-bottom: none;
}

.search-poster {
    width: 40px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 0.75rem;
}

.search-info {
    flex: 1;
}

.search-title {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.search-meta {
    color: #cccccc;
    font-size: 0.8rem;
}

/* Filter Section Styles */
.filter-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #1a1f2e;
    border-radius: 10px;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.5rem;
    background-color: #2a2f3e;
    color: #ffffff;
    border: 1px solid #3a3f4e;
    border-radius: 5px;
    min-width: 150px;
    font-family: inherit;
}

.filter-select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Page Header Styles */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.page-description {
    color: #cccccc;
    font-size: 1.1rem;
    margin-top: 0.5rem;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.trailer-button {
    background-color: #666;
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.trailer-button:hover {
    background-color: #777;
}

/* Footer Styles */
.site-footer {
    background: rgba(26, 31, 46, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #4f46e5;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section a:hover {
    color: #4f46e5;
    transform: translateX(3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #2a2f3e;
    color: #cccccc;
}

/* Detail Page Styles */
.detail-page {
    margin-top: -80px; /* Offset header */
    min-height: 100vh;
}

.detail-container {
    max-width: 1400px;
    margin: 0 auto;
}

.detail-backdrop {
    position: relative;
    min-height: 70vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.detail-overlay {
    background: linear-gradient(
        to right,
        rgba(15, 20, 25, 0.95) 0%,
        rgba(15, 20, 25, 0.8) 50%,
        rgba(15, 20, 25, 0.95) 100%
    );
    min-height: 70vh;
    display: flex;
    align-items: center;
    padding: 100px 2rem 2rem;
}

.detail-header {
    display: flex;
    gap: 3rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.detail-poster {
    flex-shrink: 0;
}

.poster-image {
    width: 300px;
    height: 450px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.detail-info {
    flex: 1;
    color: #ffffff;
}

.detail-title {
    font-size: 3rem;
    color: #4f46e5;
    margin-bottom: 2rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.detail-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-label {
    font-weight: bold;
    color: #1e3a8a;
    min-width: 120px;
}

.info-value {
    color: #ffffff;
}

.detail-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.watch-button, .trailer-button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.watch-button {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    color: #ffffff;
}

.watch-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.trailer-button {
    background-color: #1e3a8a;
    color: #ffffff;
}

.trailer-button:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
}

.detail-body {
    padding: 3rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.detail-section {
    margin-bottom: 3rem;
}

.detail-section .section-title {
    font-size: 2rem;
    color: #4f46e5;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    position: relative;
    font-weight: 600;
}

.detail-section .section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    border-radius: 2px;
}

.detail-overview {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #cccccc;
    text-align: justify;
}

.detail-cast {
    font-size: 1rem;
    line-height: 1.6;
    color: #cccccc;
}

.stream-container {
    background-color: #1a1f2e;
    border-radius: 10px;
    padding: 2rem;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stream-placeholder {
    color: #888;
    font-size: 1.2rem;
    text-align: center;
}

.stream-container iframe {
    border-radius: 10px;
}

.error {
    text-align: center;
    color: #ef4444;
    font-size: 1.2rem;
    padding: 2rem;
    font-weight: 500;
}

/* Responsive Design for Detail Page */
@media (max-width: 1024px) {
    .detail-header {
        gap: 2rem;
    }

    .poster-image {
        width: 250px;
        height: 375px;
    }

    .detail-title {
        font-size: 2.5rem;
    }

    .detail-meta {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .detail-header {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .poster-image {
        width: 220px;
        height: 330px;
        margin: 0 auto;
    }

    .detail-title {
        font-size: 2rem;
    }

    .detail-meta {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.2rem;
    }

    .info-label {
        min-width: auto;
        font-size: 0.9rem;
    }

    .info-value {
        font-size: 0.9rem;
    }

    .detail-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .watch-button, .trailer-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .detail-overlay {
        padding: 120px 1rem 2rem;
        min-height: 60vh;
    }

    .detail-body {
        padding: 2rem 1rem;
    }

    .detail-section .section-title {
        font-size: 1.5rem;
    }

    .stream-container {
        padding: 1rem;
        min-height: 300px;
    }
}

@media (max-width: 480px) {
    .poster-image {
        width: 180px;
        height: 270px;
    }

    .detail-title {
        font-size: 1.5rem;
    }

    .detail-actions {
        flex-direction: column;
        width: 100%;
    }

    .watch-button, .trailer-button {
        width: 100%;
        justify-content: center;
    }

    .detail-overlay {
        padding: 100px 0.5rem 1rem;
    }

    .detail-body {
        padding: 1.5rem 0.5rem;
    }

    .stream-container {
        margin: 0 -0.5rem;
        border-radius: 0;
    }
}

.footer-bottom p {
    margin-bottom: 0.5rem;
}
