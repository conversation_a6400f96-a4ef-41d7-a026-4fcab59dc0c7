/*
Theme Name: StreamFlix
Description: A modern streaming website theme for movies and TV shows
Version: 1.0
Author: StreamFlix Team
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #0f1419;
    color: #ffffff;
    line-height: 1.6;
}

/* Header Styles */
.site-header {
    background-color: #1a1f2e;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.site-logo {
    font-size: 2rem;
    font-weight: bold;
    color: #dc2626;
    text-decoration: none;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.main-nav a:hover {
    background-color: #dc2626;
    color: #fff;
}

.search-form {
    display: flex;
    gap: 0.5rem;
}

.search-input {
    padding: 0.5rem;
    border: none;
    border-radius: 5px;
    background-color: #1e3a8a;
    color: #ffffff;
    width: 200px;
}

.search-button {
    padding: 0.5rem 1rem;
    background-color: #dc2626;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    padding: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #1a1f2e 0%, #0f1419 100%);
    margin-bottom: 3rem;
    border-radius: 10px;
}

.hero-title {
    font-size: 3rem;
    color: #dc2626;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 2rem;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-item {
    background-color: #1a1f2e;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.content-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
}

.content-poster {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.content-info {
    padding: 1rem;
}

.content-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.content-year {
    color: #1e3a8a;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.content-rating {
    color: #cccccc;
    font-size: 0.8rem;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #dc2626;
}

.section-icon {
    color: #dc2626;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.section-title {
    font-size: 2rem;
    color: #ffffff;
}

/* Loading Spinner */
.loading {
    text-align: center;
    padding: 2rem;
    color: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-nav ul {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: #1a1f2e;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #dc2626;
}

.modal-title {
    color: #dc2626;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.modal-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.modal-poster {
    width: 200px;
    border-radius: 10px;
}

.modal-info {
    flex: 1;
}

.modal-overview {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.watch-button {
    background-color: #dc2626;
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.watch-button:hover {
    background-color: #ffed4e;
}

/* Search Dropdown Styles */
.search-form {
    position: relative;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #1a1f2e;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.search-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #2a2f3e;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-item:hover {
    background-color: #2a2f3e;
}

.search-item:last-child {
    border-bottom: none;
}

.search-poster {
    width: 40px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 0.75rem;
}

.search-info {
    flex: 1;
}

.search-title {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.search-meta {
    color: #cccccc;
    font-size: 0.8rem;
}

/* Filter Section Styles */
.filter-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #1a1f2e;
    border-radius: 10px;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.5rem;
    background-color: #2a2f3e;
    color: #ffffff;
    border: 1px solid #3a3f4e;
    border-radius: 5px;
    min-width: 150px;
    font-family: inherit;
}

.filter-select:focus {
    outline: none;
    border-color: #dc2626;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    background-color: #dc2626;
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: bold;
}

.load-more-btn:hover {
    background-color: #b91c1c;
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Page Header Styles */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.page-description {
    color: #cccccc;
    font-size: 1.1rem;
    margin-top: 0.5rem;
}

/* Modal Actions */
.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.trailer-button {
    background-color: #666;
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.trailer-button:hover {
    background-color: #777;
}

/* Footer Styles */
.site-footer {
    background-color: #1a1f2e;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    border-top: 2px solid #dc2626;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #dc2626;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #dc2626;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #2a2f3e;
    color: #cccccc;
}

/* Detail Page Styles */
.detail-page {
    margin-top: -80px; /* Offset header */
    min-height: 100vh;
}

.detail-container {
    max-width: 1400px;
    margin: 0 auto;
}

.detail-backdrop {
    position: relative;
    min-height: 70vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.detail-overlay {
    background: linear-gradient(
        to right,
        rgba(15, 20, 25, 0.95) 0%,
        rgba(15, 20, 25, 0.8) 50%,
        rgba(15, 20, 25, 0.95) 100%
    );
    min-height: 70vh;
    display: flex;
    align-items: center;
    padding: 100px 2rem 2rem;
}

.detail-header {
    display: flex;
    gap: 3rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.detail-poster {
    flex-shrink: 0;
}

.poster-image {
    width: 300px;
    height: 450px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.detail-info {
    flex: 1;
    color: #ffffff;
}

.detail-title {
    font-size: 3rem;
    color: #dc2626;
    margin-bottom: 2rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.detail-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-label {
    font-weight: bold;
    color: #1e3a8a;
    min-width: 120px;
}

.info-value {
    color: #ffffff;
}

.detail-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.watch-button, .trailer-button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.watch-button {
    background-color: #dc2626;
    color: #ffffff;
}

.watch-button:hover {
    background-color: #b91c1c;
    transform: translateY(-2px);
}

.trailer-button {
    background-color: #1e3a8a;
    color: #ffffff;
}

.trailer-button:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
}

.detail-body {
    padding: 3rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.detail-section {
    margin-bottom: 3rem;
}

.detail-section .section-title {
    font-size: 2rem;
    color: #dc2626;
    margin-bottom: 1rem;
    border-bottom: 2px solid #dc2626;
    padding-bottom: 0.5rem;
}

.detail-overview {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #cccccc;
    text-align: justify;
}

.detail-cast {
    font-size: 1rem;
    line-height: 1.6;
    color: #cccccc;
}

.stream-container {
    background-color: #1a1f2e;
    border-radius: 10px;
    padding: 2rem;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stream-placeholder {
    color: #888;
    font-size: 1.2rem;
    text-align: center;
}

.stream-container iframe {
    border-radius: 10px;
}

.error {
    text-align: center;
    color: #dc2626;
    font-size: 1.2rem;
    padding: 2rem;
}

/* Responsive Design for Detail Page */
@media (max-width: 1024px) {
    .detail-header {
        gap: 2rem;
    }

    .poster-image {
        width: 250px;
        height: 375px;
    }

    .detail-title {
        font-size: 2.5rem;
    }

    .detail-meta {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .detail-header {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .poster-image {
        width: 220px;
        height: 330px;
        margin: 0 auto;
    }

    .detail-title {
        font-size: 2rem;
    }

    .detail-meta {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.2rem;
    }

    .info-label {
        min-width: auto;
        font-size: 0.9rem;
    }

    .info-value {
        font-size: 0.9rem;
    }

    .detail-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .watch-button, .trailer-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .detail-overlay {
        padding: 120px 1rem 2rem;
        min-height: 60vh;
    }

    .detail-body {
        padding: 2rem 1rem;
    }

    .detail-section .section-title {
        font-size: 1.5rem;
    }

    .stream-container {
        padding: 1rem;
        min-height: 300px;
    }
}

@media (max-width: 480px) {
    .poster-image {
        width: 180px;
        height: 270px;
    }

    .detail-title {
        font-size: 1.5rem;
    }

    .detail-actions {
        flex-direction: column;
        width: 100%;
    }

    .watch-button, .trailer-button {
        width: 100%;
        justify-content: center;
    }

    .detail-overlay {
        padding: 100px 0.5rem 1rem;
    }

    .detail-body {
        padding: 1.5rem 0.5rem;
    }

    .stream-container {
        margin: 0 -0.5rem;
        border-radius: 0;
    }
}

.footer-bottom p {
    margin-bottom: 0.5rem;
}
