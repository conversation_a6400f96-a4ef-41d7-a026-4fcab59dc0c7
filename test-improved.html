<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamFlix - Improved Design</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header class="site-header">
        <div class="header-container">
            <a href="#" class="site-logo">StreamFlix</a>
            
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
            
            <nav class="main-nav" id="mainNav">
                <ul>
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Movies</a></li>
                    <li><a href="#">TV Shows</a></li>
                    <li><a href="#">Genres</a></li>
                </ul>
            </nav>
            
            <form class="search-form">
                <input type="search" class="search-input" placeholder="Search movies & TV shows..." />
                <button type="submit" class="search-button">Search</button>
            </form>
        </div>
    </header>

    <main class="main-content">
        <!-- Dynamic Hero Banner -->
        <div class="hero-banner" id="heroBanner">
            <!-- Hero slides will be dynamically generated -->
            <div class="hero-nav" id="heroNav">
                <!-- Navigation dots will be dynamically generated -->
            </div>
        </div>

        <!-- Fallback Hero Section -->
        <div class="hero-section" id="heroFallback">
            <h1 class="hero-title">Welcome to StreamFlix</h1>
            <p class="hero-subtitle">Discover and stream the latest movies and TV shows with our improved design!</p>
        </div>

        <section class="featured-content">
            <div class="section-header">
                <span class="section-icon">⭐</span>
                <h2 class="section-title">Featured Movies</h2>
            </div>
            
            <div class="content-grid">
                <!-- Sample movie cards -->
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg" alt="The Dark Knight" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">The Dark Knight</h3>
                        <div class="content-year">2008</div>
                        <div class="content-rating">⭐ 9.0/10</div>
                    </div>
                </div>
                
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/3bhkrj58Vtu7enYsRolD1fZdja1.jpg" alt="The Godfather" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">The Godfather</h3>
                        <div class="content-year">1972</div>
                        <div class="content-rating">⭐ 9.2/10</div>
                    </div>
                </div>
                
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/sF1U4EUQS8YHUYjNl3pMGNIQyr0.jpg" alt="Schindler's List" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">Schindler's List</h3>
                        <div class="content-year">1993</div>
                        <div class="content-rating">⭐ 8.9/10</div>
                    </div>
                </div>
                
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg" alt="The Shawshank Redemption" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">The Shawshank Redemption</h3>
                        <div class="content-year">1994</div>
                        <div class="content-rating">⭐ 9.3/10</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="popular-tv">
            <div class="section-header">
                <span class="section-icon">📺</span>
                <h2 class="section-title">Popular TV Shows</h2>
            </div>
            
            <div class="content-grid">
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/u3bZgnGQ9T01sWNhyveQz0wH0Hl.jpg" alt="Game of Thrones" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">Game of Thrones</h3>
                        <div class="content-year">2011</div>
                        <div class="content-rating">⭐ 9.3/10</div>
                    </div>
                </div>
                
                <div class="content-item">
                    <img src="https://image.tmdb.org/t/p/w500/ggFHVNu6YYI5L9pCfOacjizRGt.jpg" alt="Breaking Bad" class="content-poster">
                    <div class="content-info">
                        <h3 class="content-title">Breaking Bad</h3>
                        <div class="content-year">2008</div>
                        <div class="content-rating">⭐ 9.5/10</div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="load-more-container">
            <button class="load-more-btn">Load More Content</button>
        </div>
    </main>

    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>StreamFlix</h3>
                    <p>Your ultimate destination for movies and TV shows streaming with improved design.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#">Movies</a></li>
                        <li><a href="#">TV Shows</a></li>
                        <li><a href="#">Genres</a></li>
                        <li><a href="#">Top Rated</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="#">Action</a></li>
                        <li><a href="#">Comedy</a></li>
                        <li><a href="#">Drama</a></li>
                        <li><a href="#">Sci-Fi</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 StreamFlix. All rights reserved. Now with better design!</p>
            </div>
        </div>
    </footer>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mainNav = document.getElementById('mainNav');

        // Mobile menu functionality
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenuToggle.classList.toggle('active');
            mainNav.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = mainNav.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenuToggle.classList.remove('active');
                mainNav.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!mobileMenuToggle.contains(event.target) && !mainNav.contains(event.target)) {
                mobileMenuToggle.classList.remove('active');
                mainNav.classList.remove('active');
            }
        });

        // Initialize Hero Banner
        initHeroBanner();

        // Demo button functionality
        document.querySelector('.load-more-btn').addEventListener('click', function() {
            alert('Load more functionality works! No more harsh red styling.');
        });
    });

    // Hero Banner Functionality
    let currentSlide = 0;
    let heroSlides = [];
    let slideInterval;

    async function initHeroBanner() {
        try {
            // Fetch popular movies from TMDB
            const response = await fetch('https://api.themoviedb.org/3/movie/popular?api_key=3308647fabe47a844ab269e6eab19132&page=1');
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                // Get first 5 movies
                heroSlides = data.results.slice(0, 5);
                createHeroSlides();
                startSlideshow();

                // Hide fallback hero section
                document.getElementById('heroFallback').style.display = 'none';
            } else {
                // Show fallback if no data
                document.getElementById('heroBanner').style.display = 'none';
            }
        } catch (error) {
            console.error('Error fetching hero movies:', error);
            // Show fallback on error
            document.getElementById('heroBanner').style.display = 'none';
        }
    }

    function createHeroSlides() {
        const heroBanner = document.getElementById('heroBanner');
        const heroNav = document.getElementById('heroNav');

        // Clear existing content
        heroBanner.innerHTML = '';
        heroNav.innerHTML = '';

        heroSlides.forEach((movie, index) => {
            // Create slide
            const slide = document.createElement('div');
            slide.className = `hero-slide ${index === 0 ? 'active' : ''}`;
            slide.style.backgroundImage = `url(https://image.tmdb.org/t/p/original${movie.backdrop_path})`;

            // Create overlay and content
            const overlay = document.createElement('div');
            overlay.className = 'hero-overlay';

            const content = document.createElement('div');
            content.className = 'hero-content';

            // Get movie details
            const title = movie.title;
            const overview = movie.overview.length > 150 ? movie.overview.substring(0, 150) + '...' : movie.overview;
            const rating = Math.round(movie.vote_average * 10) / 10;
            const releaseYear = new Date(movie.release_date).getFullYear();

            content.innerHTML = `
                <h1 class="hero-title">${title}</h1>
                <p class="hero-subtitle">${overview}</p>
                <div class="hero-meta">
                    <div class="hero-meta-item">
                        <span>⭐</span>
                        <span>${rating}/10</span>
                    </div>
                    <div class="hero-meta-item">
                        <span>📅</span>
                        <span>${releaseYear}</span>
                    </div>
                    <div class="hero-meta-item">
                        <span>🎬</span>
                        <span>Movie</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="hero-play-btn" onclick="playMovie(${movie.id})">
                        <span>▶</span>
                        <span>Play Now</span>
                    </button>
                    <button class="hero-info-btn" onclick="showMovieInfo(${movie.id})">
                        <span>ℹ</span>
                        <span>More Info</span>
                    </button>
                </div>
            `;

            overlay.appendChild(content);
            slide.appendChild(overlay);
            heroBanner.appendChild(slide);

            // Create navigation dot
            const dot = document.createElement('div');
            dot.className = `hero-dot ${index === 0 ? 'active' : ''}`;
            dot.addEventListener('click', () => goToSlide(index));
            heroNav.appendChild(dot);
        });

        // Re-append navigation
        heroBanner.appendChild(heroNav);
    }

    function startSlideshow() {
        slideInterval = setInterval(() => {
            nextSlide();
        }, 5000); // Change slide every 5 seconds
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % heroSlides.length;
        updateSlide();
    }

    function goToSlide(index) {
        currentSlide = index;
        updateSlide();

        // Restart interval
        clearInterval(slideInterval);
        startSlideshow();
    }

    function updateSlide() {
        const slides = document.querySelectorAll('.hero-slide');
        const dots = document.querySelectorAll('.hero-dot');

        slides.forEach((slide, index) => {
            slide.classList.toggle('active', index === currentSlide);
        });

        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });
    }

    // Movie interaction functions
    function playMovie(movieId) {
        alert(`Playing movie ID: ${movieId}\nThis would redirect to the streaming page.`);
    }

    function showMovieInfo(movieId) {
        alert(`Showing info for movie ID: ${movieId}\nThis would redirect to the detail page.`);
    }
    </script>
</body>
</html>
