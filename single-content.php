<?php get_header(); ?>

<div class="detail-page">
    <div class="detail-container">
        <div id="detailContent" class="detail-content">
            <div class="loading">Loading content details...</div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    const type = urlParams.get('type');
    
    if (id && type) {
        loadContentDetails(id, type);
    } else {
        $('#detailContent').html('<div class="error">Invalid content ID or type.</div>');
    }
    
    function loadContentDetails(id, type) {
        const endpoint = type === 'movies' ? 'movie' : 'tv';
        
        // Load main details
        $.ajax({
            url: `https://api.themoviedb.org/3/${endpoint}/${id}?api_key=3308647fabe47a844ab269e6eab19132&append_to_response=credits,videos`,
            type: 'GET',
            success: function(data) {
                renderDetailPage(data, type);
                
                // Load additional data for movies (budget info)
                if (type === 'movies') {
                    loadMovieFinancials(id);
                }
            },
            error: function() {
                $('#detailContent').html('<div class="error">Failed to load content details.</div>');
            }
        });
    }
    
    function renderDetailPage(data, type) {
        const title = data.title || data.name;
        const overview = data.overview || 'No overview available.';
        const posterPath = data.poster_path ? 
            'https://image.tmdb.org/t/p/w500' + data.poster_path : 
            '<?php echo get_template_directory_uri(); ?>/images/no-poster.jpg';
        const backdropPath = data.backdrop_path ? 
            'https://image.tmdb.org/t/p/original' + data.backdrop_path : '';
        const releaseDate = data.release_date || data.first_air_date;
        const rating = Math.round(data.vote_average * 10) / 10;
        const runtime = data.runtime || 
            (data.episode_run_time && data.episode_run_time[0]) || 'N/A';
        const genres = data.genres ? data.genres.map(g => g.name).join(', ') : 'N/A';
        const voteCount = data.vote_count || 0;
        const popularity = Math.round(data.popularity) || 0;
        
        // Cast information
        const cast = data.credits && data.credits.cast ? 
            data.credits.cast.slice(0, 10).map(actor => actor.name).join(', ') : 'N/A';
        
        // Director/Creator information
        let director = 'N/A';
        if (data.credits && data.credits.crew) {
            const directorObj = data.credits.crew.find(person => person.job === 'Director');
            director = directorObj ? directorObj.name : 'N/A';
        }
        
        // Additional info for TV shows
        let additionalInfo = '';
        if (type === 'tv') {
            const seasons = data.number_of_seasons || 'N/A';
            const episodes = data.number_of_episodes || 'N/A';
            const status = data.status || 'N/A';
            const networks = data.networks ? data.networks.map(n => n.name).join(', ') : 'N/A';
            
            additionalInfo = `
                <div class="info-item">
                    <span class="info-label">Seasons:</span>
                    <span class="info-value">${seasons}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Episodes:</span>
                    <span class="info-value">${episodes}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">${status}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Networks:</span>
                    <span class="info-value">${networks}</span>
                </div>
            `;
        }
        
        const detailHtml = `
            <div class="detail-backdrop" style="background-image: url('${backdropPath}');">
                <div class="detail-overlay">
                    <div class="detail-header">
                        <div class="detail-poster">
                            <img src="${posterPath}" alt="${title}" class="poster-image">
                        </div>
                        <div class="detail-info">
                            <h1 class="detail-title">${title}</h1>
                            <div class="detail-meta">
                                <div class="info-item">
                                    <span class="info-label">${type === 'movies' ? 'Release Date' : 'First Air Date'}:</span>
                                    <span class="info-value">${releaseDate || 'N/A'}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Rating:</span>
                                    <span class="info-value">⭐ ${rating}/10 (${voteCount} votes)</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">${type === 'movies' ? 'Runtime' : 'Episode Runtime'}:</span>
                                    <span class="info-value">${runtime} minutes</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Genres:</span>
                                    <span class="info-value">${genres}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">${type === 'movies' ? 'Director' : 'Creator'}:</span>
                                    <span class="info-value">${director}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Popularity:</span>
                                    <span class="info-value">${popularity}</span>
                                </div>
                                ${additionalInfo}
                                <div id="financialInfo"></div>
                            </div>
                            <div class="detail-actions">
                                <button class="watch-button" onclick="playContent(${data.id}, '${type}')">
                                    <span class="play-icon">▶</span> Watch Now
                                </button>
                                <button class="trailer-button" onclick="watchTrailer(${data.id}, '${type}')">
                                    <span class="trailer-icon">🎬</span> Watch Trailer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detail-body">
                <div class="detail-section">
                    <h2 class="section-title">Story</h2>
                    <p class="detail-overview">${overview}</p>
                </div>
                
                <div class="detail-section">
                    <h2 class="section-title">Cast</h2>
                    <p class="detail-cast">${cast}</p>
                </div>
                
                <div class="detail-section">
                    <h2 class="section-title">Watch Online</h2>
                    <div id="streamPlayer" class="stream-container">
                        <p class="stream-placeholder">Click "Watch Now" to start streaming</p>
                    </div>
                </div>
            </div>
        `;
        
        $('#detailContent').html(detailHtml);
    }
    
    function loadMovieFinancials(movieId) {
        // Note: TMDB API doesn't always have budget/revenue data, but we'll try to get it
        $.ajax({
            url: `https://api.themoviedb.org/3/movie/${movieId}?api_key=3308647fabe47a844ab269e6eab19132`,
            type: 'GET',
            success: function(data) {
                if (data.budget || data.revenue) {
                    const budget = data.budget ? '$' + (data.budget / 1000000).toFixed(1) + 'M' : 'N/A';
                    const revenue = data.revenue ? '$' + (data.revenue / 1000000).toFixed(1) + 'M' : 'N/A';
                    
                    const financialHtml = `
                        <div class="info-item">
                            <span class="info-label">Budget:</span>
                            <span class="info-value">${budget}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Box Office:</span>
                            <span class="info-value">${revenue}</span>
                        </div>
                    `;
                    
                    $('#financialInfo').html(financialHtml);
                }
            }
        });
    }
    
    // Global function to play content
    window.playContent = function(id, type) {
        let streamUrl;
        
        if (type === 'movies') {
            streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1`;
        } else {
            streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1&s=1&e=1`;
        }
        
        // Embed the stream in the page instead of opening new window
        const streamHtml = `
            <iframe src="${streamUrl}" 
                    width="100%" 
                    height="500" 
                    frameborder="0" 
                    allowfullscreen>
            </iframe>
        `;
        
        $('#streamPlayer').html(streamHtml);
        
        // Scroll to the player
        $('html, body').animate({
            scrollTop: $('#streamPlayer').offset().top - 100
        }, 1000);
    };
    
    // Global function to watch trailer
    window.watchTrailer = function(id, type) {
        const endpoint = type === 'movies' ? 'movie' : 'tv';
        
        $.ajax({
            url: `https://api.themoviedb.org/3/${endpoint}/${id}/videos?api_key=3308647fabe47a844ab269e6eab19132`,
            type: 'GET',
            success: function(data) {
                if (data.results && data.results.length > 0) {
                    const trailer = data.results.find(video => video.type === 'Trailer') || data.results[0];
                    if (trailer && trailer.site === 'YouTube') {
                        const trailerUrl = `https://www.youtube.com/embed/${trailer.key}`;
                        const trailerHtml = `
                            <iframe src="${trailerUrl}" 
                                    width="100%" 
                                    height="400" 
                                    frameborder="0" 
                                    allowfullscreen>
                            </iframe>
                        `;
                        $('#streamPlayer').html(trailerHtml);
                        
                        // Scroll to the player
                        $('html, body').animate({
                            scrollTop: $('#streamPlayer').offset().top - 100
                        }, 1000);
                    }
                } else {
                    alert('No trailer available for this content.');
                }
            },
            error: function() {
                alert('Failed to load trailer.');
            }
        });
    };
});
</script>

<?php get_footer(); ?>
