<?php get_header(); ?>

<div class="page-header">
    <div class="section-header">
        <span class="section-icon">🔍</span>
        <h1 class="section-title">Search Results</h1>
    </div>
    <?php if (get_search_query()) : ?>
        <p class="page-description">Results for: "<?php echo esc_html(get_search_query()); ?>"</p>
    <?php endif; ?>
</div>

<div class="filter-section">
    <div class="filter-controls">
        <select id="searchFilter" class="filter-select">
            <option value="multi">All</option>
            <option value="movie">Movies</option>
            <option value="tv">TV Shows</option>
            <option value="person">People</option>
        </select>
        
        <select id="searchSort" class="filter-select">
            <option value="popularity.desc">Most Popular</option>
            <option value="vote_average.desc">Highest Rated</option>
            <option value="release_date.desc">Latest</option>
        </select>
    </div>
</div>

<section class="search-results">
    <div id="searchGrid" class="content-grid">
        <div class="loading">Searching...</div>
    </div>
    
    <div class="load-more-container">
        <button id="loadMoreSearch" class="load-more-btn" style="display: none;">Load More Results</button>
    </div>
</section>

<script>
jQuery(document).ready(function($) {
    const searchQuery = '<?php echo esc_js(get_search_query()); ?>';
    let currentPage = 1;
    let isLoading = false;
    let hasMoreContent = true;
    
    if (searchQuery) {
        performSearch(searchQuery, 1, true);
    } else {
        $('#searchGrid').html('<p>Please enter a search term.</p>');
    }
    
    // Filter change handlers
    $('#searchFilter, #searchSort').on('change', function() {
        if (searchQuery) {
            currentPage = 1;
            hasMoreContent = true;
            performSearch(searchQuery, 1, true);
        }
    });
    
    // Load more button
    $('#loadMoreSearch').on('click', function() {
        if (!isLoading && hasMoreContent && searchQuery) {
            currentPage++;
            performSearch(searchQuery, currentPage, false);
        }
    });
    
    function performSearch(query, page, replace = false) {
        if (isLoading) return;
        
        isLoading = true;
        $('#loadMoreSearch').text('Loading...').prop('disabled', true);
        
        if (replace) {
            $('#searchGrid').html('<div class="loading">Searching...</div>');
        }
        
        const searchType = $('#searchFilter').val();
        const sort = $('#searchSort').val();
        
        let url = `https://api.themoviedb.org/3/search/${searchType}?api_key=3308647fabe47a844ab269e6eab19132&query=${encodeURIComponent(query)}&page=${page}`;
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(data) {
                if (data && data.results) {
                    let resultsHtml = '';
                    
                    if (data.results.length === 0 && replace) {
                        resultsHtml = '<div class="no-results"><p>No results found for your search.</p></div>';
                    } else {
                        data.results.forEach(function(item) {
                            if (item.media_type === 'person') {
                                // Handle person results differently
                                const name = item.name;
                                const profilePath = item.profile_path ? 
                                    'https://image.tmdb.org/t/p/w500' + item.profile_path : 
                                    '<?php echo get_template_directory_uri(); ?>/images/no-poster.jpg';
                                const knownFor = item.known_for_department || 'Acting';
                                
                                resultsHtml += `
                                    <div class="content-item person-item" data-id="${item.id}" data-type="person">
                                        <img src="${profilePath}" alt="${name}" class="content-poster">
                                        <div class="content-info">
                                            <h3 class="content-title">${name}</h3>
                                            <div class="content-year">${knownFor}</div>
                                            <div class="content-rating">⭐ ${item.popularity ? Math.round(item.popularity) : 'N/A'}</div>
                                        </div>
                                    </div>
                                `;
                            } else {
                                // Handle movie/TV results
                                const title = item.title || item.name;
                                const type = item.media_type || (item.title ? 'movie' : 'tv');
                                const date = item.release_date || item.first_air_date;
                                const year = date ? new Date(date).getFullYear() : 'N/A';
                                const posterPath = item.poster_path ? 
                                    'https://image.tmdb.org/t/p/w500' + item.poster_path : 
                                    '<?php echo get_template_directory_uri(); ?>/images/no-poster.jpg';
                                const rating = Math.round(item.vote_average * 10) / 10;
                                
                                resultsHtml += `
                                    <div class="content-item" data-id="${item.id}" data-type="${type === 'movie' ? 'movies' : 'tv'}">
                                        <img src="${posterPath}" alt="${title}" class="content-poster">
                                        <div class="content-info">
                                            <h3 class="content-title">${title}</h3>
                                            <div class="content-year">${year}</div>
                                            <div class="content-rating">⭐ ${rating}/10</div>
                                        </div>
                                    </div>
                                `;
                            }
                        });
                    }
                    
                    if (replace) {
                        $('#searchGrid').html(resultsHtml);
                    } else {
                        $('#searchGrid').append(resultsHtml);
                    }
                    
                    bindContentItemEvents();
                    
                    // Check if there are more pages
                    hasMoreContent = page < data.total_pages;
                    
                    if (hasMoreContent && data.results.length > 0) {
                        $('#loadMoreSearch').show().text('Load More Results').prop('disabled', false);
                    } else {
                        $('#loadMoreSearch').hide();
                    }
                } else {
                    if (replace) {
                        $('#searchGrid').html('<p>No results found.</p>');
                    }
                }
                
                isLoading = false;
            },
            error: function() {
                $('#searchGrid').html('<p>Error performing search. Please try again.</p>');
                isLoading = false;
            }
        });
    }
    
    function bindContentItemEvents() {
        $('.content-item:not(.person-item)').off('click').on('click', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            openModal(id, type);
        });
        
        $('.person-item').off('click').on('click', function() {
            const id = $(this).data('id');
            // For person items, you could implement a person details modal
            // For now, we'll just show an alert
            alert('Person details not implemented yet.');
        });
    }
});
</script>

<style>
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: #cccccc;
}

.no-results p {
    font-size: 1.2rem;
}

.person-item .content-poster {
    border-radius: 50%;
    width: 150px;
    height: 150px;
    object-fit: cover;
    margin: 0 auto 1rem;
    display: block;
}

.person-item {
    text-align: center;
}

.person-item .content-info {
    padding: 0.5rem;
}
</style>

<?php get_footer(); ?>
