<?php get_header(); ?>

<div class="page-header">
    <div class="section-header">
        <span class="section-icon">📺</span>
        <h1 class="section-title">TV Shows</h1>
    </div>
    <p class="page-description">Explore the best TV series and shows</p>
</div>

<div class="filter-section">
    <div class="filter-controls">
        <select id="tvGenre" class="filter-select">
            <option value="">All Genres</option>
            <option value="10759">Action & Adventure</option>
            <option value="16">Animation</option>
            <option value="35">Comedy</option>
            <option value="80">Crime</option>
            <option value="99">Documentary</option>
            <option value="18">Drama</option>
            <option value="10751">Family</option>
            <option value="10762">Kids</option>
            <option value="9648">Mystery</option>
            <option value="10763">News</option>
            <option value="10764">Reality</option>
            <option value="10765">Sci-Fi & Fantasy</option>
            <option value="10766">Soap</option>
            <option value="10767">Talk</option>
            <option value="10768">War & Politics</option>
            <option value="37">Western</option>
        </select>
        
        <select id="tvSort" class="filter-select">
            <option value="popularity.desc">Most Popular</option>
            <option value="first_air_date.desc">Latest Release</option>
            <option value="vote_average.desc">Highest Rated</option>
            <option value="vote_count.desc">Most Voted</option>
        </select>
        
        <select id="tvYear" class="filter-select">
            <option value="">All Years</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
            <option value="2021">2021</option>
            <option value="2020">2020</option>
        </select>
        
        <select id="tvStatus" class="filter-select">
            <option value="">All Status</option>
            <option value="0">Returning Series</option>
            <option value="1">Planned</option>
            <option value="2">In Production</option>
            <option value="3">Ended</option>
            <option value="4">Cancelled</option>
            <option value="5">Pilot</option>
        </select>
    </div>
</div>

<section class="tv-content">
    <div id="tvGrid" class="content-grid">
        <div class="loading">Loading TV shows...</div>
    </div>
    
    <div class="load-more-container">
        <button id="loadMoreTV" class="load-more-btn" style="display: none;">Load More TV Shows</button>
    </div>
</section>

<script>
jQuery(document).ready(function($) {
    let currentPage = 1;
    let isLoading = false;
    let hasMoreContent = true;
    
    // Load initial TV shows
    loadTVShows(1, true);
    
    // Filter change handlers
    $('#tvGenre, #tvSort, #tvYear, #tvStatus').on('change', function() {
        currentPage = 1;
        hasMoreContent = true;
        loadTVShows(1, true);
    });
    
    // Load more button
    $('#loadMoreTV').on('click', function() {
        if (!isLoading && hasMoreContent) {
            currentPage++;
            loadTVShows(currentPage, false);
        }
    });
    
    function loadTVShows(page, replace = false) {
        if (isLoading) return;
        
        isLoading = true;
        $('#loadMoreTV').text('Loading...').prop('disabled', true);
        
        if (replace) {
            $('#tvGrid').html('<div class="loading">Loading TV shows...</div>');
        }
        
        const genre = $('#tvGenre').val();
        const sort = $('#tvSort').val();
        const year = $('#tvYear').val();
        const status = $('#tvStatus').val();
        
        let url = `https://api.themoviedb.org/3/discover/tv?api_key=3308647fabe47a844ab269e6eab19132&page=${page}&sort_by=${sort}`;
        
        if (genre) {
            url += `&with_genres=${genre}`;
        }
        
        if (year) {
            url += `&first_air_date_year=${year}`;
        }
        
        if (status) {
            url += `&with_status=${status}`;
        }
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(data) {
                if (data && data.results) {
                    let tvHtml = '';
                    
                    data.results.forEach(function(show) {
                        const title = show.name;
                        const year = show.first_air_date ? new Date(show.first_air_date).getFullYear() : 'N/A';
                        const posterPath = show.poster_path ? 
                            'https://image.tmdb.org/t/p/w500' + show.poster_path : 
                            '<?php echo get_template_directory_uri(); ?>/images/no-poster.jpg';
                        const rating = Math.round(show.vote_average * 10) / 10;
                        
                        tvHtml += `
                            <div class="content-item" data-id="${show.id}" data-type="tv">
                                <img src="${posterPath}" alt="${title}" class="content-poster">
                                <div class="content-info">
                                    <h3 class="content-title">${title}</h3>
                                    <div class="content-year">${year}</div>
                                    <div class="content-rating">⭐ ${rating}/10</div>
                                </div>
                            </div>
                        `;
                    });
                    
                    if (replace) {
                        $('#tvGrid').html(tvHtml);
                    } else {
                        $('#tvGrid').append(tvHtml);
                    }
                    
                    bindContentItemEvents();
                    
                    // Check if there are more pages
                    hasMoreContent = page < data.total_pages;
                    
                    if (hasMoreContent) {
                        $('#loadMoreTV').show().text('Load More TV Shows').prop('disabled', false);
                    } else {
                        $('#loadMoreTV').hide();
                    }
                } else {
                    if (replace) {
                        $('#tvGrid').html('<p>No TV shows found.</p>');
                    }
                }
                
                isLoading = false;
            },
            error: function() {
                $('#tvGrid').html('<p>Error loading TV shows. Please try again.</p>');
                isLoading = false;
            }
        });
    }
    
    function bindContentItemEvents() {
        $('.content-item').off('click').on('click', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            openModal(id, type);
        });
    }
    
    function openModal(id, type) {
        // Redirect to detail page instead of opening modal
        const detailUrl = `${window.location.origin}${window.location.pathname}?content=detail&id=${id}&type=${type}`;
        window.location.href = detailUrl;
    }
    
    // Close modal
    $('.close, .modal').on('click', function(e) {
        if (e.target === this) {
            $('#contentModal').hide();
        }
    });
});

function watchContent(id, type) {
    const streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1&s=1&e=1`;
    window.open(streamUrl, '_blank');
}
</script>

<?php get_footer(); ?>
