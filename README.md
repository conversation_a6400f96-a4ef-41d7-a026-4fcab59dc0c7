# StreamFlix - WordPress Streaming Website

A modern WordPress theme for streaming movies and TV shows, similar to the design shown in your reference image but with English text and enhanced functionality.

## Features

- **Dark Theme Design**: Modern dark interface matching the reference image
- **TMDB API Integration**: Fetches real movie and TV show data
- **SuperEmbed Streaming**: Integrated streaming functionality
- **Responsive Design**: Works on all devices
- **Search Functionality**: Live search with dropdown results
- **Filter Options**: Genre, year, rating, and status filters
- **Modal Details**: Detailed information for each movie/TV show
- **Infinite Scroll**: Load more content seamlessly

## Installation

1. **Upload Theme Files**:
   - Copy all theme files to your WordPress themes directory: `/wp-content/themes/streamflix/`

2. **Activate Theme**:
   - Go to WordPress Admin → Appearance → Themes
   - Activate the "StreamFlix" theme

3. **Create Required Pages**:
   The theme will automatically create these pages when activated:
   - Movies page (`/movies`)
   - TV Shows page (`/tv-shows`)

4. **Set Up Navigation**:
   - Go to WordPress Admin → Appearance → Menus
   - Create a new menu and assign it to "Primary Menu"
   - Add the Movies and TV Shows pages to your menu

## Configuration

### TMDB API
The theme uses TMDB API key: `3308647fabe47a844ab269e6eab19132`
This is already configured in `functions.php`.

### SuperEmbed Integration
Streaming functionality uses SuperEmbed (multiembed.mov) which is already integrated.

## File Structure

```
streamflix/
├── style.css              # Main stylesheet
├── functions.php          # Theme functions and API integration
├── index.php             # Homepage template
├── header.php            # Header template
├── footer.php            # Footer template
├── page-movies.php       # Movies page template
├── page-tv-shows.php     # TV Shows page template
├── search.php            # Search results template
├── js/
│   └── main.js          # JavaScript functionality
├── images/
│   └── no-poster.jpg    # Placeholder for missing posters
└── README.md            # This file
```

## Key Features Explained

### Homepage
- Displays featured movies and popular TV shows
- Hero section with site branding
- Grid layout for content items

### Movies Page
- Browse popular movies
- Filter by genre, year, and sort options
- Load more functionality
- Click any movie to see details and watch

### TV Shows Page
- Browse popular TV series
- Filter by genre, year, status, and sort options
- Load more functionality
- Click any show to see details and watch

### Search Functionality
- Live search with dropdown suggestions
- Dedicated search results page
- Filter search results by type (movies, TV, people)

### Streaming Integration
- **Movies**: Direct streaming via SuperEmbed
- **TV Shows**: Default to Season 1, Episode 1
- **Trailers**: YouTube integration for trailers

## Customization

### Colors
Main colors used in the theme:
- Background: `#0f1419`
- Secondary Background: `#1a1f2e`
- Accent Color: `#ffd700` (gold)
- Text: `#ffffff`
- Secondary Text: `#cccccc`

### API Configuration
To change the TMDB API key, edit `functions.php`:
```php
define('TMDB_API_KEY', 'your-api-key-here');
```

### Streaming Service
To change the streaming service, modify the `get_superembed_url()` function in `functions.php`.

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Dependencies
- WordPress 5.0+
- jQuery (included with WordPress)
- Internet connection for TMDB API and streaming

## Troubleshooting

### Movies/TV Shows Not Loading
1. Check internet connection
2. Verify TMDB API key is valid
3. Check browser console for JavaScript errors

### Streaming Not Working
1. Ensure SuperEmbed service is accessible
2. Check if content is available on the streaming service
3. Try different browser or disable ad blockers

### Search Not Working
1. Check TMDB API connectivity
2. Verify JavaScript is enabled
3. Check browser console for errors

## Support
This theme integrates with:
- **TMDB API**: For movie and TV show data
- **SuperEmbed**: For streaming functionality
- **YouTube**: For trailers

## License
This theme is for educational and personal use. Please respect TMDB API terms of service and streaming service policies.

## Credits
- **TMDB**: Movie and TV show data
- **SuperEmbed**: Streaming functionality
- **Design**: Inspired by modern streaming platforms
