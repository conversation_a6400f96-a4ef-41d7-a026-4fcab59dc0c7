/**
 * StreamFlix Main JavaScript
 */

jQuery(document).ready(function($) {
    
    // Global variables
    let searchTimeout;
    
    // Initialize search functionality
    initializeSearch();

    // Initialize modal functionality
    initializeModal();

    // Initialize infinite scroll for homepage
    initializeInfiniteScroll();


    
    function initializeSearch() {
        const searchForm = $('.search-form');
        const searchInput = $('.search-input');
        
        // Live search functionality
        searchInput.on('input', function() {
            const query = $(this).val().trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(function() {
                    performSearch(query);
                }, 500);
            }
        });
        
        // Handle search form submission
        searchForm.on('submit', function(e) {
            e.preventDefault();
            const query = searchInput.val().trim();
            if (query.length >= 3) {
                performSearch(query);
            }
        });
    }
    
    function performSearch(query) {
        // Show search results in a dropdown or redirect to search page
        $.ajax({
            url: `https://api.themoviedb.org/3/search/multi?api_key=3308647fabe47a844ab269e6eab19132&query=${encodeURIComponent(query)}`,
            type: 'GET',
            success: function(data) {
                displaySearchResults(data.results);
            },
            error: function() {
                console.error('Search failed');
            }
        });
    }
    
    function displaySearchResults(results) {
        // Create search results dropdown
        let searchDropdown = $('#searchDropdown');
        
        if (searchDropdown.length === 0) {
            searchDropdown = $('<div id="searchDropdown" class="search-dropdown"></div>');
            $('.search-form').append(searchDropdown);
        }
        
        if (results.length === 0) {
            searchDropdown.html('<div class="search-item">No results found</div>').show();
            return;
        }
        
        let resultsHtml = '';
        results.slice(0, 5).forEach(function(item) {
            const title = item.title || item.name;
            const type = item.media_type;
            const year = item.release_date || item.first_air_date;
            const yearDisplay = year ? new Date(year).getFullYear() : '';
            const posterPath = item.poster_path ? 
                'https://image.tmdb.org/t/p/w92' + item.poster_path : '';
            
            resultsHtml += `
                <div class="search-item" data-id="${item.id}" data-type="${type}">
                    ${posterPath ? `<img src="${posterPath}" alt="${title}" class="search-poster">` : ''}
                    <div class="search-info">
                        <div class="search-title">${title}</div>
                        <div class="search-meta">${type === 'movie' ? 'Movie' : 'TV Show'} ${yearDisplay ? '(' + yearDisplay + ')' : ''}</div>
                    </div>
                </div>
            `;
        });
        
        searchDropdown.html(resultsHtml).show();
        
        // Bind click events to search results
        $('.search-item').on('click', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            searchDropdown.hide();
            openModal(id, type === 'movie' ? 'movies' : 'tv');
        });
    }
    
    function initializeModal() {
        // Close modal when clicking outside or on close button
        $(document).on('click', '.close, .modal', function(e) {
            if (e.target === this) {
                $('.modal').hide();
            }
        });
        
        // Close modal with escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                $('.modal').hide();
            }
        });
        
        // Hide search dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-form').length) {
                $('#searchDropdown').hide();
            }
        });
    }
    
    function initializeInfiniteScroll() {
        // Only on homepage
        if (!$('body').hasClass('home')) return;
        
        $(window).on('scroll', function() {
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 1000) {
                // Load more content when near bottom
                loadMoreHomeContent();
            }
        });
    }
    
    function loadMoreHomeContent() {
        // This would be implemented based on specific needs
        // For now, it's a placeholder
    }
    
    // Global function to open detail page
    window.openModal = function(id, type) {
        // Redirect to detail page instead of opening modal
        const detailUrl = `${window.location.origin}${window.location.pathname}?content=detail&id=${id}&type=${type}`;
        window.location.href = detailUrl;
    };
    
    // Global function to watch content
    window.watchContent = function(id, type) {
        let streamUrl;
        
        if (type === 'movies') {
            streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1`;
        } else {
            // For TV shows, default to season 1 episode 1
            streamUrl = `https://multiembed.mov/?video_id=${id}&tmdb=1&s=1&e=1`;
        }
        
        // Open in new window/tab
        window.open(streamUrl, '_blank', 'width=1200,height=700,scrollbars=yes,resizable=yes');
    };
    
    // Global function to watch trailer
    window.watchTrailer = function(id, type) {
        const endpoint = type === 'movies' ? 'movie' : 'tv';
        
        $.ajax({
            url: `https://api.themoviedb.org/3/${endpoint}/${id}/videos?api_key=3308647fabe47a844ab269e6eab19132`,
            type: 'GET',
            success: function(data) {
                if (data.results && data.results.length > 0) {
                    // Find trailer or teaser
                    const trailer = data.results.find(video => 
                        video.type === 'Trailer' && video.site === 'YouTube'
                    ) || data.results.find(video => 
                        video.type === 'Teaser' && video.site === 'YouTube'
                    ) || data.results[0];
                    
                    if (trailer && trailer.site === 'YouTube') {
                        const youtubeUrl = `https://www.youtube.com/watch?v=${trailer.key}`;
                        window.open(youtubeUrl, '_blank');
                    }
                } else {
                    alert('No trailer available for this content.');
                }
            },
            error: function() {
                alert('Failed to load trailer information.');
            }
        });
    };
    
    // Utility function to format date
    window.formatDate = function(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    
    // Utility function to format runtime
    window.formatRuntime = function(minutes) {
        if (!minutes || minutes === 'N/A') return 'N/A';
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };
});

// Add some CSS for search dropdown
const searchDropdownCSS = `
<style>
.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #1a1f2e;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    max-height: 300px;
    overflow-y: auto;
}

.search-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #2a2f3e;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-item:hover {
    background-color: #2a2f3e;
}

.search-item:last-child {
    border-bottom: none;
}

.search-poster {
    width: 40px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 0.75rem;
}

.search-info {
    flex: 1;
}

.search-title {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.search-meta {
    color: #cccccc;
    font-size: 0.8rem;
}

.filter-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #1a1f2e;
    border-radius: 10px;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-select {
    padding: 0.5rem;
    background-color: #2a2f3e;
    color: #ffffff;
    border: 1px solid #3a3f4e;
    border-radius: 5px;
    min-width: 150px;
}

.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    background-color: #ffd700;
    color: #000;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.load-more-btn:hover {
    background-color: #ffed4e;
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-description {
    color: #cccccc;
    font-size: 1.1rem;
    margin-top: 0.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.trailer-button {
    background-color: #666;
    color: #fff;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.trailer-button:hover {
    background-color: #777;
}

@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
    }
    
    .filter-select {
        min-width: auto;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}
</style>
`;

    // Inject the CSS
    $('head').append(searchDropdownCSS);

});
